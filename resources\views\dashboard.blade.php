<x-feed-layout>

    <!-- Create Post Card -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 p-4 mb-4">
        <div class="flex items-center space-x-3">
            <img class="h-10 w-10 rounded-full" src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ auth()->user()->name }}">
            <div class="flex-1">
                <button onclick="openPersonalPostModal()" class="w-full text-left px-4 py-3 bg-custom-lightest hover:bg-custom-lightest hover:bg-opacity-80 rounded-full text-custom-second-darkest transition-colors">
                    What's on your mind, {{ auth()->user()->name }}?
                </button>
            </div>
        </div>
        <div class="flex items-center justify-between mt-3 pt-3 border-t border-custom-second-darkest border-opacity-20">
            <button onclick="openPersonalPostModal()" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm font-medium">Photo/Video</span>
            </button>
            <button onclick="openPersonalPostModal()" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm font-medium">Event</span>
            </button>
            <a href="{{ route('scholarships.create') }}" class="flex items-center space-x-2 px-4 py-2 text-custom-second-darkest hover:bg-custom-lightest rounded-lg transition-colors">
                <svg class="w-5 h-5 text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" />
                </svg>
                <span class="text-sm font-medium">Scholarship</span>
            </a>
        </div>
    </div>

    <!-- Filter Pills -->
    <!-- <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 p-3 mb-4">
        <div class="flex flex-wrap gap-2">
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-green rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                All Posts
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Events
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Scholarships
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Announcements
            </button>
            <button class="px-4 py-2 text-sm font-medium text-custom-darkest bg-custom-lightest rounded-full hover:bg-custom-second-darkest hover:text-custom-lightest focus:outline-none focus:ring-2 focus:ring-custom-green transition-colors">
                Organizations
            </button>
        </div>
    </div> -->

    <!-- Enhanced Filter Bar -->
    @include('components.feed-filter-bar')

    <!-- Main Feed -->
    <div class="space-y-6" id="posts-container">
        @forelse($posts as $feedItem)
            @if($feedItem->type === 'post')
                <x-post-card :post="$feedItem->data" />
            @elseif($feedItem->type === 'share')
                <x-shared-post-card :share="$feedItem->data" />
            @endif
        @empty
            <!-- Empty State -->
            <div id="empty-state" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10m0 0V6a2 2 0 00-2-2H9a2 2 0 00-2 2v2m10 0v10a2 2 0 01-2 2H9a2 2 0 01-2-2V8m10 0H7m0 0V6a2 2 0 012-2h10a2 2 0 012 2v2M7 8v10a2 2 0 002 2h10a2 2 0 002-2V8" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No posts yet</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating your first post!</p>
                <div class="mt-6">
                    <button onclick="openPersonalPostModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-custom-green hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Create Post
                    </button>
                </div>
            </div>
        @endforelse

        <!-- Pagination -->
        @if($posts->hasPages())
            <div class="pagination-container flex justify-center py-6">
                <nav aria-label="Pagination" class="inline-flex -space-x-px">
                    {{ $posts->links('components.custom-pagination') }}
                </nav>
            </div>
        @endif
    </div>

    <!-- Personal Post Creation Modal -->
    @include('components.personal-post-creation-modal')









</x-feed-layout>
