<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\PostMethod;
use App\Models\Tag;
use App\Models\Organization;
use App\Models\Group;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class PostController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Get regular posts
        $posts = Post::with([
                'user',
                'organization',
                'group',
                'postMethod',
                'tags',
                'comments' => function ($query) {
                    $query->with('user', 'reactions', 'replies.user', 'replies.reactions')->whereNull('parent_id')->latest();
                },
                'likes',
                'shares'
            ])
            ->published()
            ->visibleInFeed()
            ->latest('published_at')
            ->get();

        // Get shared posts (timeline shares only) - filtered by privacy scope
        $shares = \App\Models\Share::with([
                'user',
                'likes',
                'comments' => function ($query) {
                    $query->with('user', 'reactions', 'replies.user', 'replies.reactions')->whereNull('parent_id')->latest();
                },
                'post.user',
                'post.organization',
                'post.comments' => function ($query) {
                    $query->with('user', 'reactions', 'replies.user', 'replies.reactions')->whereNull('parent_id')->latest();
                },
                'post.likes',
                'post.shares'
            ])
            ->where('share_type', 'direct')
            ->visibleTo(auth()->user())
            ->whereHas('post', function($query) {
                $query->published();
            })
            ->latest('created_at')
            ->get();

        // Combine and sort by date
        $feedItems = collect();

        // Add posts with type indicator
        foreach ($posts as $post) {
            $feedItems->push((object)[
                'type' => 'post',
                'data' => $post,
                'created_at' => $post->published_at,
            ]);
        }

        // Add shares with type indicator
        foreach ($shares as $share) {
            $feedItems->push((object)[
                'type' => 'share',
                'data' => $share,
                'created_at' => $share->created_at,
            ]);
        }

        // Sort by date (newest first) and paginate
        $feedItems = $feedItems->sortByDesc('created_at');

        // Manual pagination
        $perPage = 10;
        $currentPage = request()->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $paginatedItems = $feedItems->slice($offset, $perPage);

        $posts = new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedItems,
            $feedItems->count(),
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return view('posts.index', compact('posts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $organizations = collect();

        // Get organizations where user is a member
        if (auth()->check()) {
            $organizations = auth()->user()->activeOrganizations()->get();
        }

        // Determine context-based post method (default to user)
        $postMethodSlug = 'user';
        $postMethod = PostMethod::where('slug', $postMethodSlug)->with('activeTags')->first();

        return view('posts.create', compact('organizations', 'postMethod'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {


        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:tags,id',
            'organization_id' => 'nullable|exists:organizations,id',
            'group_id' => 'nullable|exists:groups,id',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'attachments' => 'nullable|array|max:10',
            'attachments.*' => 'file|max:51200', // 50MB max
            'facebook_embed_url' => 'nullable|url',
            'is_pinned' => 'nullable',
            'status' => 'nullable|string',
        ]);

        // Convert empty IDs to null
        if (empty($validated['organization_id'])) {
            $validated['organization_id'] = null;
        }
        if (empty($validated['group_id'])) {
            $validated['group_id'] = null;
        }

        // Ensure only one of organization_id or group_id is set
        if ($validated['organization_id'] && $validated['group_id']) {
            return back()->withErrors(['error' => 'Post cannot belong to both organization and group.']);
        }

        // Automatically determine post method based on context
        $postMethodSlug = 'user'; // Default to user post
        if ($validated['group_id']) {
            $postMethodSlug = 'group';
        } elseif ($validated['organization_id']) {
            $postMethodSlug = 'organization';
        }

        $postMethod = PostMethod::where('slug', $postMethodSlug)->first();
        if (!$postMethod) {
            return back()->withErrors(['error' => 'Invalid post context.']);
        }

        $validated['post_method_id'] = $postMethod->id;

        // Set default type based on post method
        $validated['type'] = 'general'; // Default type for all posts

        // Check if user can post to the selected organization
        if ($validated['organization_id']) {
            $organization = Organization::findOrFail($validated['organization_id']);

            if (!$organization->userCanPost(auth()->user())) {
                return back()->withErrors(['organization_id' => 'You are not authorized to post to this organization. Only officers and presidents can create posts.']);
            }
        }

        // Check if user can post to the selected group
        if ($validated['group_id']) {
            $group = Group::findOrFail($validated['group_id']);

            if (!$group->hasActiveMember(auth()->user())) {
                return back()->withErrors(['group_id' => 'You must be an active member to post in this group.']);
            }
        }

        $validated['user_id'] = auth()->id();

        // Set default status if not provided
        if (!isset($validated['status'])) {
            $validated['status'] = 'published';
        }

        // Handle post approval for groups
        $needsApproval = false;
        if ($validated['group_id']) {
            $group = Group::find($validated['group_id']);
            if ($group->post_approval === 'required' && !$group->userCanModerate(auth()->user())) {
                $validated['approval_status'] = 'pending';
                $validated['status'] = 'published'; // Published but pending approval
                $validated['published_at'] = now();
                $needsApproval = true;
            } else {
                $validated['approval_status'] = 'approved';
                $validated['approved_at'] = now();
                $validated['approved_by'] = auth()->id();
                $validated['published_at'] = $validated['status'] === 'published' ? now() : null;
            }
        } else {
            // For organizations and personal posts, auto-approve
            $validated['approval_status'] = 'approved';
            $validated['approved_at'] = now();
            $validated['approved_by'] = auth()->id();
            $validated['published_at'] = $validated['status'] === 'published' ? now() : null;
        }

        // Convert is_pinned to boolean if it exists
        if (isset($validated['is_pinned'])) {
            $validated['is_pinned'] = filter_var($validated['is_pinned'], FILTER_VALIDATE_BOOLEAN);
        } else {
            $validated['is_pinned'] = false;
        }

        // Handle image uploads
        if ($request->hasFile('images')) {
            $imagePaths = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('posts/images', 'public');
                $imagePaths[] = $path;
            }
            $validated['images'] = $imagePaths;
        }

        $post = Post::create($validated);

        // Sync tags if provided
        if (!empty($validated['tags'])) {
            $post->syncTags($validated['tags']);
        }

        // Send notification if post needs approval
        if ($needsApproval && isset($group)) {
            $group->moderators()->each(function ($moderator) use ($post, $group) {
                $moderator->notify(new \App\Notifications\GroupPostPendingApproval($post, $group, auth()->user()));
            });
        }

        // Handle file attachments
        if ($request->hasFile('attachments')) {
            if ($validated['group_id']) {
                // Group post attachments - check group settings
                $group = Group::find($validated['group_id']);
                if ($group && $group->allow_file_sharing) {
                    foreach ($request->file('attachments') as $file) {
                        $originalName = $file->getClientOriginalName();
                        $extension = $file->getClientOriginalExtension();
                        $size = $file->getSize();

                        // Check file size limit
                        if ($size > ($group->max_file_size_mb * 1024 * 1024)) {
                            continue; // Skip files that are too large
                        }

                        // Check allowed file types
                        if ($group->allowed_file_types && !in_array(strtolower($extension), $group->allowed_file_types)) {
                            continue; // Skip disallowed file types
                        }

                        $path = $file->store('groups/attachments', 'public');
                        $filename = basename($path); // Extract the generated filename from the path

                        $post->fileAttachments()->create([
                            'filename' => $filename,
                            'file_path' => $path,
                            'original_filename' => $originalName,
                            'file_type' => strtolower($extension),
                            'file_size' => $size,
                            'mime_type' => $file->getMimeType(),
                        ]);
                    }
                }
            } else {
                // Normal post attachments - use default settings
                foreach ($request->file('attachments') as $file) {
                    $originalName = $file->getClientOriginalName();
                    $extension = $file->getClientOriginalExtension();
                    $size = $file->getSize();

                    // Default file size limit (10MB)
                    if ($size > (10 * 1024 * 1024)) {
                        continue; // Skip files that are too large
                    }

                    // Default allowed file types (common document and media types)
                    $allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'zip', 'rar', '7z'];
                    if (!in_array(strtolower($extension), $allowedTypes)) {
                        continue; // Skip disallowed file types
                    }

                    // Determine storage path
                    if ($validated['organization_id']) {
                        $path = $file->store('organizations/attachments', 'public');
                    } else {
                        $path = $file->store('posts/attachments', 'public');
                    }

                    $filename = basename($path); // Extract the generated filename from the path

                    $post->fileAttachments()->create([
                        'filename' => $filename,
                        'file_path' => $path,
                        'original_filename' => $originalName,
                        'file_type' => strtolower($extension),
                        'file_size' => $size,
                        'mime_type' => $file->getMimeType(),
                    ]);
                }
            }
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Post created successfully!',
                'post' => $post->load(['user', 'organization', 'group'])
            ]);
        }

        // Redirect based on post type
        if ($post->group_id) {
            $message = $post->approval_status === 'pending'
                ? 'Post submitted for approval!'
                : 'Post created successfully!';
            return redirect()->route('groups.show', $post->group)
                ->with('success', $message);
        } elseif ($post->organization_id) {
            // Check if organization is in page mode
            if ($post->organization->is_page_mode) {
                return redirect()->route('pages.show', $post->organization)
                    ->with('success', 'Post created successfully!');
            } else {
                return redirect()->route('organizations.show', $post->organization)
                    ->with('success', 'Post created successfully!');
            }
        } else {
            return redirect()->route('posts.show', $post)
                ->with('success', 'Post created successfully!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Post $post)
    {
        $post->load([
            'user',
            'organization',
            'group',
            'postMethod',
            'tags',
            'comments' => function ($query) {
                $query->with('user', 'reactions', 'replies.user', 'replies.reactions')->whereNull('parent_id')->latest();
            },
            'likes.user',
            'shares.user'
        ]);

        return view('posts.show', compact('post'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Post $post)
    {
        // Check if user can edit this post
        if ($post->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $post->load('fileAttachments', 'tags', 'postMethod');
        $organizations = auth()->user()->activeOrganizations()->get();

        return view('posts.edit', compact('post', 'organizations'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Post $post)
    {
        // Check if user can edit this post
        if ($post->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:tags,id',
            'organization_id' => 'nullable|exists:organizations,id',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'attachments' => 'nullable|array|max:10',
            'attachments.*' => 'file|max:51200', // 50MB max
            'facebook_embed_url' => 'nullable|url',
            'is_pinned' => 'boolean',
            'status' => ['required', Rule::in(['published', 'draft'])],
            'remove_images' => 'nullable|array',
            'remove_images.*' => 'string',
            'remove_attachments' => 'nullable|array',
            'remove_attachments.*' => 'integer',
        ]);

        // Check organization membership if changing organization
        if (isset($validated['organization_id']) && $validated['organization_id'] && $validated['organization_id'] !== $post->organization_id) {
            $organization = Organization::findOrFail($validated['organization_id']);

            if (!$organization->userCanPost(auth()->user())) {
                return back()->withErrors(['organization_id' => 'You are not authorized to post to this organization. Only officers and presidents can create posts.']);
            }
        }

        // Automatically determine post method based on context
        $postMethodSlug = 'user'; // Default to user post
        if ($post->group_id) {
            $postMethodSlug = 'group';
        } elseif ($validated['organization_id'] ?? $post->organization_id) {
            $postMethodSlug = 'organization';
        }

        $postMethod = PostMethod::where('slug', $postMethodSlug)->first();
        if ($postMethod) {
            $validated['post_method_id'] = $postMethod->id;
        }

        // Set default type
        $validated['type'] = 'general';

        // Handle published_at timestamp
        if ($validated['status'] === 'published' && !$post->published_at) {
            $validated['published_at'] = now();
        } elseif ($validated['status'] === 'draft') {
            $validated['published_at'] = null;
        }

        // Handle image removal
        $currentImages = $post->images ?? [];
        if ($request->has('remove_images')) {
            foreach ($request->remove_images as $imageToRemove) {
                if (in_array($imageToRemove, $currentImages)) {
                    Storage::disk('public')->delete($imageToRemove);
                    $currentImages = array_filter($currentImages, fn($img) => $img !== $imageToRemove);
                }
            }
        }

        // Handle new image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('posts/images', 'public');
                $currentImages[] = $path;
            }
        }

        $validated['images'] = array_values($currentImages);

        // Handle attachment removal
        if ($request->has('remove_attachments')) {
            foreach ($request->remove_attachments as $attachmentId) {
                $attachment = $post->fileAttachments()->find($attachmentId);
                if ($attachment) {
                    // Delete the physical file
                    if (Storage::disk('public')->exists($attachment->file_path)) {
                        Storage::disk('public')->delete($attachment->file_path);
                    }
                    // Delete the database record
                    $attachment->delete();
                }
            }
        }

        // Handle new attachment uploads
        if ($request->hasFile('attachments')) {
            if ($post->group_id) {
                // Group post attachments - check group settings
                $group = Group::find($post->group_id);
                if ($group && $group->allow_file_sharing) {
                    foreach ($request->file('attachments') as $file) {
                        $originalName = $file->getClientOriginalName();
                        $extension = $file->getClientOriginalExtension();
                        $size = $file->getSize();

                        // Check file size limit
                        if ($size > ($group->max_file_size_mb * 1024 * 1024)) {
                            continue; // Skip files that are too large
                        }

                        // Check allowed file types
                        if ($group->allowed_file_types && !in_array(strtolower($extension), $group->allowed_file_types)) {
                            continue; // Skip disallowed file types
                        }

                        $path = $file->store('groups/attachments', 'public');
                        $filename = basename($path);

                        $post->fileAttachments()->create([
                            'filename' => $filename,
                            'file_path' => $path,
                            'original_filename' => $originalName,
                            'file_type' => strtolower($extension),
                            'file_size' => $size,
                            'mime_type' => $file->getMimeType(),
                        ]);
                    }
                }
            } else {
                // Normal post attachments - use default settings
                foreach ($request->file('attachments') as $file) {
                    $originalName = $file->getClientOriginalName();
                    $extension = $file->getClientOriginalExtension();
                    $size = $file->getSize();

                    // Default file size limit (10MB)
                    if ($size > (10 * 1024 * 1024)) {
                        continue; // Skip files that are too large
                    }

                    // Default allowed file types
                    $allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'zip', 'rar', '7z'];
                    if (!in_array(strtolower($extension), $allowedTypes)) {
                        continue; // Skip disallowed file types
                    }

                    // Determine storage path
                    if ($post->organization_id) {
                        $path = $file->store('organizations/attachments', 'public');
                    } else {
                        $path = $file->store('posts/attachments', 'public');
                    }

                    $filename = basename($path);

                    $post->fileAttachments()->create([
                        'filename' => $filename,
                        'file_path' => $path,
                        'original_filename' => $originalName,
                        'file_type' => strtolower($extension),
                        'file_size' => $size,
                        'mime_type' => $file->getMimeType(),
                    ]);
                }
            }
        }

        $post->update($validated);

        // Sync tags if provided
        if (isset($validated['tags'])) {
            $post->syncTags($validated['tags']);
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Post updated successfully!',
                'post' => $post->load(['user', 'organization', 'tags'])
            ]);
        }

        return redirect()->route('posts.show', $post)
            ->with('success', 'Post updated successfully!');
    }

    /**
     * Get tags for a specific post method (API endpoint)
     */
    public function getTagsByPostMethod(Request $request)
    {
        $postMethodId = $request->get('post_method_id');

        if (!$postMethodId) {
            return response()->json(['error' => 'Post method ID is required'], 400);
        }

        $tags = Tag::where('post_method_id', $postMethodId)
                   ->where('is_active', true)
                   ->orderBy('name')
                   ->get(['id', 'name', 'slug', 'color']);

        return response()->json(['tags' => $tags]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Post $post)
    {
        // Check if user can delete this post
        if ($post->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        // Delete associated images
        if ($post->images) {
            foreach ($post->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $post->delete();

        return redirect()->route('dashboard')
            ->with('success', 'Post deleted successfully!');
    }

    /**
     * Toggle like on a post
     */
    public function toggleLike(Post $post)
    {
        $user = auth()->user();
        $like = $post->likes()->where('user_id', $user->id)->first();

        if ($like) {
            $like->delete();
            $liked = false;
        } else {
            $post->likes()->create(['user_id' => $user->id]);
            $liked = true;
        }

        return response()->json([
            'success' => true,
            'liked' => $liked,
            'likes_count' => $post->likes()->count()
        ]);
    }

    /**
     * Filter posts based on criteria
     */
    public function filter(Request $request)
    {
        $currentUser = auth()->user();

        // Get regular posts
        $postsQuery = Post::with([
                'user',
                'organization',
                'group',
                'postMethod',
                'tags',
                'comments' => function ($query) {
                    $query->with('user', 'reactions', 'replies.user', 'replies.reactions')->whereNull('parent_id')->latest();
                },
                'likes',
                'shares',
                'fileAttachments'
            ])
            ->published()
            ->visibleInFeed();

        // Apply search filter
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $postsQuery->where(function($query) use ($searchTerm) {
                $query->where('title', 'like', "%{$searchTerm}%")
                      ->orWhere('content', 'like', "%{$searchTerm}%")
                      ->orWhereHas('tags', function($tagQuery) use ($searchTerm) {
                          $tagQuery->where('name', 'like', "%{$searchTerm}%");
                      });
            });
        }

        // Apply post type/source filter
        if ($request->filled('post_type') && $request->post_type !== 'all') {
            switch ($request->post_type) {
                case 'user':
                    $postsQuery->whereNull('organization_id')->whereNull('group_id');
                    break;
                case 'organization':
                    $postsQuery->whereNotNull('organization_id');
                    break;
                case 'group':
                    $postsQuery->whereNotNull('group_id');
                    break;
            }
        }

        // Apply tags filter
        if ($request->filled('tags') && is_array($request->tags)) {
            $postsQuery->whereHas('tags', function($query) use ($request) {
                $query->whereIn('tags.id', $request->tags);
            });
        }

        // Apply date range filter
        if ($request->filled('date_range') && $request->date_range !== 'all') {
            switch ($request->date_range) {
                case 'today':
                    $postsQuery->whereDate('published_at', today());
                    break;
                case 'week':
                    $postsQuery->whereBetween('published_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $postsQuery->whereBetween('published_at', [now()->startOfMonth(), now()->endOfMonth()]);
                    break;
                case 'custom':
                    if ($request->filled('date_from')) {
                        $postsQuery->whereDate('published_at', '>=', $request->date_from);
                    }
                    if ($request->filled('date_to')) {
                        $postsQuery->whereDate('published_at', '<=', $request->date_to);
                    }
                    break;
            }
        }

        // Apply privacy filter (simplified - you may need to adjust based on your privacy model)
        if ($request->filled('privacy') && $request->privacy !== 'all') {
            switch ($request->privacy) {
                case 'public':
                    // Assuming posts without organization/group are public
                    $postsQuery->whereNull('organization_id')->whereNull('group_id');
                    break;
                case 'org_members':
                    $postsQuery->whereNotNull('organization_id');
                    break;
                case 'private':
                    $postsQuery->where('user_id', $currentUser->id);
                    break;
            }
        }

        // Apply group filter
        if ($request->filled('group_id')) {
            $postsQuery->where('group_id', $request->group_id);
        }

        // Filter by following if requested (legacy support)
        if ($request->has('following') && $request->following === 'true') {
            $followingUserIds = $currentUser->following()->pluck('users.id')->toArray();
            $followingUserIds[] = $currentUser->id; // Include own posts
            $postsQuery->whereIn('user_id', $followingUserIds);
        }

        // Get shared posts (timeline shares only) - filtered by privacy scope
        $sharesQuery = \App\Models\Share::with([
                'user',
                'likes',
                'comments' => function ($query) {
                    $query->with('user', 'reactions', 'replies.user', 'replies.reactions')->whereNull('parent_id')->latest();
                },
                'post.user',
                'post.organization',
                'post.postMethod',
                'post.tags',
                'post.comments' => function ($query) {
                    $query->with('user', 'reactions', 'replies.user', 'replies.reactions')->whereNull('parent_id')->latest();
                },
                'post.likes',
                'post.shares',
                'post.fileAttachments'
            ])
            ->where('share_type', 'direct')
            ->visibleTo(auth()->user())
            ->whereHas('post', function($query) {
                $query->published()->visibleInFeed();
            });

        // Filter shares by following if requested
        if ($request->has('following') && $request->following === 'true') {
            $followingUserIds = $currentUser->following()->pluck('users.id')->toArray();
            $followingUserIds[] = $currentUser->id; // Include own shares
            $sharesQuery->whereIn('user_id', $followingUserIds);
        }

        // Apply filters to both queries
        // Support multiple type filters
        if ($request->has('types') && is_array($request->types) && !empty($request->types)) {
            $postsQuery->whereIn('type', $request->types);
            $sharesQuery->whereHas('post', function($query) use ($request) {
                $query->whereIn('type', $request->types);
            });
        } elseif ($request->has('type') && $request->type !== 'all') {
            // Backward compatibility for single type filter
            $postsQuery->where('type', $request->type);
            $sharesQuery->whereHas('post', function($query) use ($request) {
                $query->where('type', $request->type);
            });
        }

        // Support multiple organization filters
        if ($request->has('organization_filters') && is_array($request->organization_filters) && !empty($request->organization_filters)) {
            $orgFilters = $request->organization_filters;

            $postsQuery->where(function($query) use ($orgFilters) {
                foreach ($orgFilters as $filter) {
                    if ($filter === 'personal') {
                        $query->orWhereNull('organization_id');
                    } elseif ($filter === 'organizations') {
                        $query->orWhereNotNull('organization_id');
                    }
                }
            });

            $sharesQuery->whereHas('post', function($query) use ($orgFilters) {
                $query->where(function($subQuery) use ($orgFilters) {
                    foreach ($orgFilters as $filter) {
                        if ($filter === 'personal') {
                            $subQuery->orWhereNull('organization_id');
                        } elseif ($filter === 'organizations') {
                            $subQuery->orWhereNotNull('organization_id');
                        }
                    }
                });
            });
        } elseif ($request->has('organization_filter')) {
            // Backward compatibility for single organization filter
            if ($request->organization_filter === 'personal') {
                $postsQuery->whereNull('organization_id');
                $sharesQuery->whereHas('post', function($query) {
                    $query->whereNull('organization_id');
                });
            } elseif ($request->organization_filter === 'organizations') {
                $postsQuery->whereNotNull('organization_id');
                $sharesQuery->whereHas('post', function($query) {
                    $query->whereNotNull('organization_id');
                });
            }
        }

        if ($request->has('with_images') && $request->with_images === 'true') {
            $postsQuery->whereNotNull('images')
                      ->where(function($q) {
                          $q->where('images', '!=', '[]')
                            ->where('images', '!=', 'null');
                      });
            $sharesQuery->whereHas('post', function($query) {
                $query->whereNotNull('images')
                      ->where(function($q) {
                          $q->where('images', '!=', '[]')
                            ->where('images', '!=', 'null');
                      });
            });
        }

        if ($request->has('pinned') && $request->pinned === 'true') {
            $postsQuery->where('is_pinned', true);
            $sharesQuery->whereHas('post', function($query) {
                $query->where('is_pinned', true);
            });
        }

        // New filter options
        $showPosts = true;
        $showShares = true;

        if ($request->has('shared_posts') && $request->shared_posts === 'true') {
            // If shared posts filter is active, we need to determine what to show
            if (!$request->has('group_posts') || $request->group_posts !== 'true') {
                // Only shared posts, no regular posts
                $showPosts = false;
            }
        }

        if ($request->has('group_posts') && $request->group_posts === 'true') {
            // Filter to only show group posts
            $postsQuery->whereNotNull('group_id');
            $sharesQuery->whereHas('post', function($query) {
                $query->whereNotNull('group_id');
            });
        }

        // Post method filter
        if ($request->has('post_method_id') && $request->post_method_id) {
            $postsQuery->where('post_method_id', $request->post_method_id);
            $sharesQuery->whereHas('post', function($query) use ($request) {
                $query->where('post_method_id', $request->post_method_id);
            });
        }

        // Tag filters
        if ($request->has('tags') && is_array($request->tags) && !empty($request->tags)) {
            $postsQuery->withTags($request->tags);
            $sharesQuery->whereHas('post', function($query) use ($request) {
                $query->withTags($request->tags);
            });
        }

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $postsQuery->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('content', 'like', "%{$searchTerm}%");
            });
            $sharesQuery->where(function($q) use ($searchTerm) {
                $q->where('message', 'like', "%{$searchTerm}%")
                  ->orWhereHas('post', function($query) use ($searchTerm) {
                      $query->where('title', 'like', "%{$searchTerm}%")
                            ->orWhere('content', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // Get posts and shares based on filter options
        $posts = $showPosts ? $postsQuery->latest('published_at')->get() : collect();
        $shares = $showShares ? $sharesQuery->latest('created_at')->get() : collect();

        // Combine and sort by date
        $feedItems = collect();

        // Add posts with type indicator (only if showing posts)
        if ($showPosts) {
            foreach ($posts as $post) {
                $feedItems->push((object)[
                    'type' => 'post',
                    'data' => $post,
                    'created_at' => $post->published_at,
                ]);
            }
        }

        // Add shares with type indicator (only if showing shares)
        if ($showShares) {
            foreach ($shares as $share) {
                $feedItems->push((object)[
                    'type' => 'share',
                    'data' => $share,
                    'created_at' => $share->created_at,
                ]);
            }
        }

        // Sort by date (newest first) and paginate
        $feedItems = $feedItems->sortByDesc('created_at');

        // Manual pagination
        $perPage = 10;
        $currentPage = $request->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $paginatedItems = $feedItems->slice($offset, $perPage);

        $paginatedFeedItems = new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedItems,
            $feedItems->count(),
            $perPage,
            $currentPage,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        // Return filtered posts as HTML for AJAX
        $html = '';
        foreach ($paginatedFeedItems as $feedItem) {
            if ($feedItem->type === 'post') {
                $html .= view('components.post-card', ['post' => $feedItem->data])->render();
            } elseif ($feedItem->type === 'share') {
                $html .= view('components.shared-post-card', ['share' => $feedItem->data])->render();
            }
        }

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => [
                'current_page' => $paginatedFeedItems->currentPage(),
                'last_page' => $paginatedFeedItems->lastPage(),
                'total' => $paginatedFeedItems->total(),
                'has_more' => $paginatedFeedItems->hasMorePages(),
                'links' => $paginatedFeedItems->links()->render()
            ],
            'count' => $paginatedFeedItems->count(),
            'total' => $paginatedFeedItems->total()
        ]);
    }
}
