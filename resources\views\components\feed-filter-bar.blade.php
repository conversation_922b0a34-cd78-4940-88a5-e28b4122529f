{{-- Enhanced Feed Filter Bar Component --}}
<div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest border-opacity-20 p-3 md:p-4 mb-6 relative" x-data="feedFilterBar()">
    {{-- Mobile Filter Toggle --}}
    <div class="md:hidden mb-3">
        <button @click="showMobileFilters = !showMobileFilters"
                class="w-full flex items-center justify-between px-4 py-2 bg-gray-50 rounded-lg text-sm font-medium text-gray-700">
            <span>🔍 Filters & Search</span>
            <svg :class="showMobileFilters ? 'rotate-180' : ''" class="h-5 w-5 transition-transform">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
        </button>
    </div>

    {{-- Filter Content --}}
    <div :class="showMobileFilters ? 'block' : 'hidden md:block'"
    {{-- Search Box --}}
    <div class="mb-4">
        <div class="relative">
            <input type="text"
                   id="post-search"
                   x-model="filters.search"
                   @input.debounce.300ms="applyFilters()"
                   placeholder="🔍 Search posts... [event, tuition, org name]"
                   class="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-custom-green focus:border-custom-green text-sm">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </div>
            <button x-show="filters.search.length > 0" 
                    @click="filters.search = ''; applyFilters()"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
    </div>

    {{-- Filter Categories --}}
    <div class="space-y-3 md:space-y-4">
        {{-- Post Type/Source Filter --}}
        <div class="flex flex-wrap items-start md:items-center gap-2 md:gap-3">
            <span class="text-xs md:text-sm font-medium text-gray-700 min-w-fit w-full md:w-auto mb-1 md:mb-0">📝 Post Type:</span>
            <div class="flex flex-wrap gap-1 md:gap-2 w-full md:w-auto">
                <button @click="setPostTypeFilter('all')"
                        :class="filters.postType === 'all' ? 'bg-custom-green text-white border-custom-green' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'"
                        class="px-2 md:px-4 py-1 md:py-2 rounded-full text-xs md:text-sm font-medium border transition-colors">
                    🔘 All
                </button>
                <button @click="setPostTypeFilter('user')"
                        :class="filters.postType === 'user' ? 'bg-custom-green text-white border-custom-green' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'"
                        class="px-2 md:px-4 py-1 md:py-2 rounded-full text-xs md:text-sm font-medium border transition-colors">
                    👤 <span class="hidden sm:inline">User Posts</span><span class="sm:hidden">Users</span>
                </button>
                <button @click="setPostTypeFilter('organization')"
                        :class="filters.postType === 'organization' ? 'bg-custom-green text-white border-custom-green' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'"
                        class="px-2 md:px-4 py-1 md:py-2 rounded-full text-xs md:text-sm font-medium border transition-colors">
                    🏛️ <span class="hidden sm:inline">Org Posts</span><span class="sm:hidden">Orgs</span>
                </button>
                <button @click="setPostTypeFilter('group')"
                        :class="filters.postType === 'group' ? 'bg-custom-green text-white border-custom-green' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'"
                        class="px-2 md:px-4 py-1 md:py-2 rounded-full text-xs md:text-sm font-medium border transition-colors">
                    👥 <span class="hidden sm:inline">Group Posts</span><span class="sm:hidden">Groups</span>
                </button>
            </div>
        </div>

        {{-- Tags Filter --}}
        <div class="flex flex-wrap items-start gap-3">
            <span class="text-sm font-medium text-gray-700 min-w-fit mt-2">🏷️ Tags:</span>
            <div class="flex-1">
                {{-- Tag Filter Toggle --}}
                <button @click="showTagFilters = !showTagFilters"
                        class="px-4 py-2 rounded-lg text-sm font-medium border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors mb-2">
                    <span x-text="showTagFilters ? 'Hide Tags' : 'Show Tags'"></span>
                    <svg :class="showTagFilters ? 'rotate-180' : ''" class="inline-block ml-1 h-4 w-4 transition-transform">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                </button>
                
                {{-- Selected Tags Display --}}
                <div x-show="selectedTags.length > 0" class="flex flex-wrap gap-1 mb-2">
                    <template x-for="tag in selectedTags" :key="tag.id">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-custom-green text-white">
                            <span x-text="tag.name"></span>
                            <button @click="removeTag(tag.id)" class="ml-1 hover:text-gray-200">
                                <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </span>
                    </template>
                </div>

                {{-- Tag Selection Grid --}}
                <div x-show="showTagFilters" x-transition class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 p-3 bg-gray-50 rounded-lg">
                    <template x-for="tag in availableTags" :key="tag.id">
                        <label class="flex items-center space-x-2 cursor-pointer hover:bg-white p-2 rounded">
                            <input type="checkbox" 
                                   :value="tag.id"
                                   @change="toggleTag(tag)"
                                   :checked="selectedTags.some(t => t.id === tag.id)"
                                   class="rounded border-gray-300 text-custom-green focus:ring-custom-green">
                            <span class="text-sm" x-text="tag.name"></span>
                        </label>
                    </template>
                </div>
            </div>
        </div>

        {{-- Date Posted Filter --}}
        <div class="flex flex-wrap items-center gap-3">
            <span class="text-sm font-medium text-gray-700 min-w-fit">🗓️ Date:</span>
            <div class="relative">
                <select x-model="filters.dateRange" 
                        @change="applyFilters()"
                        class="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:ring-custom-green focus:border-custom-green">
                    <option value="all">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="custom">Custom Range</option>
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                </div>
            </div>
            
            {{-- Custom Date Range Inputs --}}
            <div x-show="filters.dateRange === 'custom'" x-transition class="flex items-center gap-2">
                <input type="date" 
                       x-model="filters.dateFrom"
                       @change="applyFilters()"
                       class="border border-gray-300 rounded px-3 py-2 text-sm focus:ring-custom-green focus:border-custom-green">
                <span class="text-gray-500">to</span>
                <input type="date" 
                       x-model="filters.dateTo"
                       @change="applyFilters()"
                       class="border border-gray-300 rounded px-3 py-2 text-sm focus:ring-custom-green focus:border-custom-green">
            </div>
        </div>

        {{-- Privacy Filter --}}
        <div class="flex flex-wrap items-center gap-3">
            <span class="text-sm font-medium text-gray-700 min-w-fit">🔐 Privacy:</span>
            <div class="flex flex-wrap gap-2">
                <button @click="setPrivacyFilter('all')"
                        :class="filters.privacy === 'all' ? 'bg-custom-green text-white border-custom-green' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'"
                        class="px-3 py-1 rounded-full text-sm font-medium border transition-colors">
                    🔘 All
                </button>
                <button @click="setPrivacyFilter('public')"
                        :class="filters.privacy === 'public' ? 'bg-custom-green text-white border-custom-green' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'"
                        class="px-3 py-1 rounded-full text-sm font-medium border transition-colors">
                    🌐 Public
                </button>
                <button @click="setPrivacyFilter('org_members')"
                        :class="filters.privacy === 'org_members' ? 'bg-custom-green text-white border-custom-green' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'"
                        class="px-3 py-1 rounded-full text-sm font-medium border transition-colors">
                    👁️‍🗨️ Org Members
                </button>
                <button @click="setPrivacyFilter('private')"
                        :class="filters.privacy === 'private' ? 'bg-custom-green text-white border-custom-green' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'"
                        class="px-3 py-1 rounded-full text-sm font-medium border transition-colors">
                    🔒 Private
                </button>
            </div>
        </div>

        {{-- Group Filter --}}
        @auth
            @if(auth()->user()->groups()->count() > 0)
            <div class="flex flex-wrap items-center gap-3">
                <span class="text-sm font-medium text-gray-700 min-w-fit">👥 Group:</span>
                <div class="relative">
                    <select x-model="filters.groupId" 
                            @change="applyFilters()"
                            class="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:ring-custom-green focus:border-custom-green min-w-48">
                        <option value="">All Groups</option>
                        @foreach(auth()->user()->groups as $group)
                            <option value="{{ $group->id }}">{{ $group->name }}</option>
                        @endforeach
                    </select>
                    <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                        <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                </div>
            </div>
            @endif
        @endauth
    </div>

    {{-- Active Filters Summary & Clear All --}}
    <div x-show="hasActiveFilters()" x-transition class="mt-4 pt-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
                <span class="text-xs text-gray-500">Active filters:</span>
                <div class="flex flex-wrap gap-1" x-html="getActiveFiltersDisplay()"></div>
            </div>
            <button @click="clearAllFilters()" 
                    class="text-sm text-gray-500 hover:text-gray-700 transition-colors">
                Clear All
            </button>
        </div>
    </div>
    </div>

    {{-- Loading State --}}
    <div x-show="loading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
        <div class="flex items-center space-x-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-custom-green"></div>
            <span class="text-sm text-gray-600">Filtering posts...</span>
        </div>
    </div>
</div>

{{-- Alpine.js Component Script --}}
<script>
function feedFilterBar() {
    return {
        loading: false,
        showTagFilters: false,
        showMobileFilters: false,
        filters: {
            search: '',
            postType: 'all',
            tags: [],
            dateRange: 'all',
            dateFrom: '',
            dateTo: '',
            privacy: 'all',
            groupId: ''
        },
        selectedTags: [],
        availableTags: @json(\App\Models\Tag::with('postMethod')->active()->get()),

        init() {
            // Load initial tags based on post type
            this.updateAvailableTags();
        },

        setPostTypeFilter(type) {
            this.filters.postType = type;
            this.updateAvailableTags();
            this.applyFilters();
        },

        setPrivacyFilter(privacy) {
            this.filters.privacy = privacy;
            this.applyFilters();
        },

        updateAvailableTags() {
            // Get all tags first
            const allTags = @json(\App\Models\Tag::with('postMethod')->active()->get());

            // Filter tags based on selected post type
            if (this.filters.postType === 'all') {
                this.availableTags = allTags;
            } else {
                // Filter tags by post method
                const postMethodMap = {
                    'user': 'user',
                    'organization': 'organization',
                    'group': 'group'
                };
                const methodSlug = postMethodMap[this.filters.postType];
                if (methodSlug) {
                    this.availableTags = allTags.filter(tag =>
                        tag.post_method && tag.post_method.slug === methodSlug
                    );
                } else {
                    this.availableTags = allTags;
                }
            }
        },

        toggleTag(tag) {
            const index = this.selectedTags.findIndex(t => t.id === tag.id);
            if (index > -1) {
                this.selectedTags.splice(index, 1);
            } else {
                this.selectedTags.push(tag);
            }
            this.filters.tags = this.selectedTags.map(t => t.id);
            this.applyFilters();
        },

        removeTag(tagId) {
            this.selectedTags = this.selectedTags.filter(t => t.id !== tagId);
            this.filters.tags = this.selectedTags.map(t => t.id);
            this.applyFilters();
        },

        hasActiveFilters() {
            return this.filters.search.length > 0 ||
                   this.filters.postType !== 'all' ||
                   this.filters.tags.length > 0 ||
                   this.filters.dateRange !== 'all' ||
                   this.filters.privacy !== 'all' ||
                   this.filters.groupId !== '';
        },

        getActiveFiltersDisplay() {
            let display = [];
            
            if (this.filters.search) {
                display.push(`<span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">Search: "${this.filters.search}"</span>`);
            }
            if (this.filters.postType !== 'all') {
                display.push(`<span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">${this.filters.postType} posts</span>`);
            }
            if (this.selectedTags.length > 0) {
                display.push(`<span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs">${this.selectedTags.length} tags</span>`);
            }
            if (this.filters.dateRange !== 'all') {
                display.push(`<span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">${this.filters.dateRange}</span>`);
            }
            if (this.filters.privacy !== 'all') {
                display.push(`<span class="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">${this.filters.privacy}</span>`);
            }
            
            return display.join(' ');
        },

        clearAllFilters() {
            this.filters = {
                search: '',
                postType: 'all',
                tags: [],
                dateRange: 'all',
                dateFrom: '',
                dateTo: '',
                privacy: 'all',
                groupId: ''
            };
            this.selectedTags = [];
            this.showTagFilters = false;
            this.updateAvailableTags();
            this.applyFilters();
        },

        async applyFilters() {
            this.loading = true;
            
            try {
                const params = new URLSearchParams();
                
                // Add all filter parameters
                if (this.filters.search) params.append('search', this.filters.search);
                if (this.filters.postType !== 'all') params.append('post_type', this.filters.postType);
                if (this.filters.tags.length > 0) {
                    this.filters.tags.forEach(tagId => params.append('tags[]', tagId));
                }
                if (this.filters.dateRange !== 'all') {
                    params.append('date_range', this.filters.dateRange);
                    if (this.filters.dateRange === 'custom') {
                        if (this.filters.dateFrom) params.append('date_from', this.filters.dateFrom);
                        if (this.filters.dateTo) params.append('date_to', this.filters.dateTo);
                    }
                }
                if (this.filters.privacy !== 'all') params.append('privacy', this.filters.privacy);
                if (this.filters.groupId) params.append('group_id', this.filters.groupId);

                const response = await fetch(`{{ route('posts.filter') }}?${params.toString()}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    // Update posts container
                    document.getElementById('posts-container').innerHTML = data.html;
                    
                    // Update pagination if exists
                    const paginationContainer = document.querySelector('.pagination-container');
                    if (paginationContainer && data.pagination.links) {
                        paginationContainer.innerHTML = data.pagination.links;
                    }
                    
                    // Dispatch custom event for other components
                    window.dispatchEvent(new CustomEvent('postsFiltered', { 
                        detail: { 
                            count: data.count, 
                            total: data.total,
                            filters: this.filters 
                        } 
                    }));
                }
            } catch (error) {
                console.error('Filter error:', error);
            } finally {
                this.loading = false;
            }
        }
    }
}
</script>
